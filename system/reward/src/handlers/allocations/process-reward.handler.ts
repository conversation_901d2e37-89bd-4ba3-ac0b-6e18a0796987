import { <PERSON>ton, Inject, IocContainer } from '@tcom/platform/lib/core/ioc';
import Logger, { LogClass } from '@tcom/platform/lib/core/logging';
import { lambdaHandler } from '@tcom/platform/lib/core';
import { RewardBalanceManager, RewardBalanceStatus } from '@tcom/platform/lib/reward';
import { RewardBalanceBatchJobItem, RewardBalanceJobManager } from '@tcom/platform/lib/reward/balances/batch';
import { StepFunctionPayloadParser } from '@tcom/platform/lib/reward/balances/utilities';
import { RewardBalanceCache } from '@tcom/platform/lib/reward/cache';
import { RewardBalanceProcessorFactory } from '../../allocations';

@Singleton
@LogClass()
class ProcessRewardHandler {
    constructor(
        @Inject private readonly balanceProcessorFactory: RewardBalanceProcessorFactory,
        @Inject private readonly parser: StepFunctionPayloadParser,
        @Inject private readonly manager: <PERSON><PERSON><PERSON>alanceManager,
        @Inject private readonly balanceCache: RewardBalanceCache,
        @Inject private readonly jobManager: RewardBalanceJobManager) {
    }

    public async execute(payload: RewardBalanceBatchJobItem): Promise<void> {
        const item = this.parser.parse(payload);
        const job = await this.jobManager.get(item.jobId, item.balance.type);

        if (!job) {
            Logger.error(`Reward balance job ${item.jobId} not found.`);
            return;
        }

        if (item.balance.status !== RewardBalanceStatus.InProgress)
            return;

        if (!item.balance.eligible) {
            await this.manager.setClosed(item.balance.id, item.balance.userId, item.balance.type);
            return;
        }

        try {
            await this.balanceCache.lockByType(item.balance.userId, item.balance.type, async () => {
                const processor = this.balanceProcessorFactory.create(item.balance.type);
                await processor.process(item.balance);
            });
            await this.jobManager.setItemComplete(item);
        } catch (error) {
            Logger.error(`Error processing reward balance ${item.balance.id}`, error);
            await this.jobManager.setItemFailed(item, error.message);
        }
    }
}

export const processReward = lambdaHandler((payload: RewardBalanceBatchJobItem) => IocContainer.get(ProcessRewardHandler).execute(payload));