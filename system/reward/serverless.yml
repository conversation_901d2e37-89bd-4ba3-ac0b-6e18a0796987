service: system-reward
enabled: ${self:custom.brand.features.rewards, "true"}

plugins:
  - serverless-plugin-resource-tagging
  - serverless-plugin-aws-exponential-backoff
  - serverless-plugin-enabled
  - serverless-webpack
  - serverless-plugin-scripts
  - serverless-plugin-warmup
  - serverless-step-functions

provider:
  versionFunctions: false
  name: aws
  runtime: nodejs20.x
  memorySize: ${self:custom.common.provider.memorySize}
  timeout: 30
  region: ${opt:region, "${self:custom.common.defaultRegion}"}
  stage: ${opt:stage}
  stackTags: ${self:custom.common.provider.stackTags}
  logRetentionInDays: ${self:custom.common.logRetention}
  iam: ${file(../serverless/iam.yml)}
  environment: ${file(../../serverless/environment.yml)}
  profile: ${self:custom.common.aws.profile}
  apiGateway:
    usagePlan: ${self:custom.common.provider.usagePlan}
  vpc: ${self:custom.common.provider.vpc}

custom:
  common: ${file(../../serverless/common.yml)}
  brand: ${file(../../brands.js):${self:custom.common.brandId}}
  webpack:
    includeModules: true
    packager: yarn
  scripts:
    hooks:
      "package:initialize": yarn build
  warmup: ${self:custom.common.warmup}

functions:
  onUserCreated:
    handler: src/handlers.onUserCreated
    timeout: 60
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - User:Created
    environment:
      FUNCTION: onUserCreated

  onUserDeleted:
    handler: src/handlers.onUserDeleted
    timeout: 60
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - User:Deleted
    environment:
      FUNCTION: onUserDeleted

  onSweepstakeEntrySetAdded:
    handler: src/handlers.onSweepstakeEntrySetAdded
    timeout: 60
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Sweepstake:EntrySet:Added
    environment:
      FUNCTION: onSweepstakeEntrySetAdded

  onHamburgerPurchased:
    handler: src/handlers.onHamburgerPurchased
    timeout: 60
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Hamburger:Purchased
    environment:
      FUNCTION: onHamburgerPurchased

  onRewardEvent:
    handler: src/handlers.onRewardEvent
    timeout: 900
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - User:Created
              - Sweepstake:EntrySet:Added
              - Payment:StatusChanged
              - User:IdentityStatusChanged
    environment:
      FUNCTION: onRewardEvent

  processCashback:
    handler: src/handlers.processCashback
    timeout: 900
    reservedConcurrency: 1
    events:
      - schedule:
          rate: cron(0 1 * * ? *)
    environment:
      FUNCTION: processCashback

  processRewardSourceEvent:
    handler: src/handlers.processRewardSourceEvent
    reservedConcurrency: 5
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt: [RewardSourceSQSQueue, Arn]
          batchSize: 10
    environment:
      FUNCTION: processRewardSourceEvent

  scheduleRewardTemplates:
    handler: src/handlers.scheduleRewardTemplates
    timeout: 60
    reservedConcurrency: 5
    events:
      - schedule: rate(1 minute)
    environment:
      FUNCTION: scheduleRewardTemplates

  scheduleRewardBalance:
    handler: src/handlers.scheduleRewardBalance
    timeout: 60
    reservedConcurrency: 5
    events:
      - schedule: cron(0/1 * * * ? *)
    environment:
      FUNCTION: scheduleRewardBalance

  onRewardAwarded:
    handler: src/handlers.onRewardAwarded
    timeout: 900
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - Reward:Awarded
    environment:
      FUNCTION: onRewardAwarded

  onRequirementSatisfied:
    handler: src/handlers.onRequirementSatisfied
    timeout: 900
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - Reward:Requirement:Satisfied
    environment:
      FUNCTION: onRequirementSatisfied

  onContributorSatisfied:
    handler: src/handlers.onContributorSatisfied
    timeout: 900
    reservedConcurrency: 5
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - Reward:Contributor:Satisfied
    environment:
      FUNCTION: onContributorSatisfied

  expireReward:
    handler: src/handlers.expireReward
    timeout: 60
    reservedConcurrency: 1
    events:
      - schedule: rate(1 minute)
    environment:
      FUNCTION: expireReward

  refreshTemplateCache:
    handler: src/handlers.refreshTemplateCache
    timeout: 600
    reservedConcurrency: 1
    environment:
      FUNCTION: refreshTemplateCache

  onDeposit:
    handler: src/handlers.onDeposit
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Payment:StatusChanged
            Type:
              - Deposit
            To:
              - Successful
    environment:
      FUNCTION: onDeposit

  onPurchase:
    handler: src/handlers.onPurchase
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Payment:StatusChanged
            Type:
              - Purchase
            To:
              - Successful
    environment:
      FUNCTION: onPurchase

  onGameEvent:
    handler: src/handlers.onGameEvent
    reservedConcurrency: 20
    timeout: 60
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - Game:BuyIn:Rollback
              - Game:Round
              - Legacy:Game:Round
    environment:
      FUNCTION: onGameEvent

  onInventoryItemClaimed:
    handler: src/handlers.onInventoryItemClaimed
    reservedConcurrency: 5
    timeout: 60
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - Inventory:Item:Claimed
    environment:
      FUNCTION: onInventoryItemClaimed

  onInventoryRewardsClaimed:
    handler: src/handlers.onInventoryRewardsClaimed
    reservedConcurrency: 5
    timeout: 60
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - Inventory:Rewards:Claimed
    environment:
      FUNCTION: onInventoryRewardsClaimed

  onSegmentUserUpdated:
    handler: src/handlers.onSegmentUserUpdated
    reservedConcurrency: 5
    timeout: 60
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          filterPolicy:
            EventType:
              - Reward:Segment:User:Updated
    environment:
      FUNCTION: onSegmentUserUpdated

  getRewards:
    handler: src/handlers.getRewards
    timeout: 900
    environment:
      FUNCTION: getRewards

  processReward:
    handler: src/handlers.processReward
    timeout: 900
    environment:
      FUNCTION: processReward

  onInventoryClaimed:
    handler: src/handlers.onInventoryClaimed
    reservedConcurrency: 20
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt: [InventoryClaimedSQSQueue, Arn]
          batchSize: 10
    environment:
      FUNCTION: onInventoryClaimed

  schedulePointAdjustments:
    handler: src/handlers.schedulePointAdjustments
    timeout: 900
    reservedConcurrency: 1
    events:
      - schedule: cron(5 0 * * ? *) # Every day at 00:05
    environment:
      FUNCTION: schedulePointAdjustments

  getPointAdjustments:
    handler: src/handlers.getPointAdjustments
    timeout: 120
    environment:
      FUNCTION: getPointAdjustments

  applyPointAdjustment:
    handler: src/handlers.applyPointAdjustment
    timeout: 900
    environment:
      FUNCTION: applyPointAdjustment

  onBatchPointAdjustmentJobComplete:
    handler: src/handlers.onBatchPointAdjustmentJobComplete
    timeout: 900
    reservedConcurrency: 1
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Reward:Points:BatchAdjustmentJob:Complete
    environment:
      FUNCTION: onBatchPointAdjustmentJobComplete

  onPointsUserStreakChanged:
    handler: src/handlers.onPointsUserStreakChanged
    timeout: 900
    reservedConcurrency: 1
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Reward:Points:User:Streak:Changed
              - Reward:Points:Streak:Ranks:Resync
    environment:
      FUNCTION: onPointsUserStreakChanged

  syncRewardBalance:
    handler: src/handlers.syncRewardBalance
    timeout: 900
    environment:
      FUNCTION: syncRewardBalance

  rerankSegmentUsers:
    handler: src/handlers.rerankSegmentUsers
    timeout: 900
    environment:
      FUNCTION: rerankSegmentUsers

stepFunctions:
  stateMachines:
    processReward:
      name: processReward
      definition:
        Comment: "Process reward balances"
        StartAt: GetRewards
        States:
          GetRewards:
            Type: "Task"
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              Payload.$: "$"
              FunctionName: "arn:aws:lambda:${self:provider.region}:${self:custom.common.aws.accountId}:function:${self:service}-${opt:stage}-getRewards"
            OutputPath: "$.Payload"
            Retry:
              - ErrorEquals:
                  - States.ALL
                IntervalSeconds: 2
                MaxAttempts: 6
                BackoffRate: 2
            Next: HasRewards
          HasRewards:
            Type: Choice
            Choices:
              - Variable: "$.rewardCount"
                NumericGreaterThan: 0
                Next: PayEachReward
            Default: Success
          PayEachReward:
            Type: Map
            Iterator:
              StartAt: PayReward
              States:
                PayReward:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    Payload.$: "$"
                    FunctionName: "arn:aws:lambda:${self:provider.region}:${self:custom.common.aws.accountId}:function:${self:service}-${opt:stage}-processReward"
                  OutputPath: "$.Payload"
                  End: true
            ItemsPath: "$.rewards"
            Next: GetRewards
            MaxConcurrency: 5
            ResultPath: null
          Success:
            Type: Succeed
      loggingConfig:
        level: ALL
        includeExecutionData: true
        destinations:
          - Fn::GetAtt: [ProcessRewardLogGroup, Arn]

    processPointAdjustments:
      name: processPointAdjustments
      definition:
        Comment: "Process reward point adjustments for users"
        StartAt: GetPointAdjustments
        States:
          GetPointAdjustments:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              Payload.$: "$"
              FunctionName: "arn:aws:lambda:${self:provider.region}:${self:custom.common.aws.accountId}:function:${self:service}-${opt:stage}-getPointAdjustments"
            OutputPath: "$.Payload"
            Retry:
              - ErrorEquals:
                  - States.ALL
                IntervalSeconds: 2
                MaxAttempts: 6
                BackoffRate: 2
            Next: HasPointAdjustments
          HasPointAdjustments:
            Type: Choice
            Choices:
              - Variable: "$.itemCount"
                NumericGreaterThan: 0
                Next: ApplyEachPointAdjustment
            Default: Success
          ApplyEachPointAdjustment:
            Type: Map
            Iterator:
              StartAt: ApplyPointAdjustment
              States:
                ApplyPointAdjustment:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    Payload.$: "$"
                    FunctionName: "arn:aws:lambda:${self:provider.region}:${self:custom.common.aws.accountId}:function:${self:service}-${opt:stage}-applyPointAdjustment"
                  OutputPath: "$.Payload"
                  End: true
            ItemsPath: "$.items"
            Next: GetPointAdjustments
            MaxConcurrency: 5
            ResultPath: null
          Success:
            Type: Succeed
      loggingConfig:
        level: ALL
        includeExecutionData: true
        destinations:
          - Fn::GetAtt: [ProcessPointAdjustmentsLogGroup, Arn]

resources:
  Resources:
    RewardSourceSQSQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: reward-source
        VisibilityTimeout: 10800

    InventoryClaimedSQSQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: inventory-claimed
        VisibilityTimeout: 10800

    ProcessRewardLogGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "/aws/stepFunctions/allocations/processReward"
        RetentionInDays: 30

    ProcessPointAdjustmentsLogGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "/aws/stepFunctions/points/processPointAdjustments"
        RetentionInDays: 30
