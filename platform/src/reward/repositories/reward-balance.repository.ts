import { Inject, <PERSON>ton } from '../../core/ioc';
import { LogClass } from '../../core/logging';
import { DBRepository } from '../../core/db';
import { convertOrdering, In } from '../../core/db/orm';
import { PagedResult } from '../../core';
import { NewRewardBalance } from '../new-reward-balance';
import { RewardBalanceMapper } from '../entities/mappers';
import { DailyRewardBalanceEntity, MonthlyRewardBalanceEntity, RankUpRewardBalanceEntity, RewardBalanceEntity, WinBackRewardBalanceEntity, WeeklyRewardBalanceEntity, DailyAllocationRewardBalanceEntity } from '../entities';
import { RewardBalanceStatus } from '../reward-balance-status';
import { RewardBalanceType } from '../reward-balance-type';
import { DailyAllocationRewardBalance, RewardBalance } from '../reward-balance';
import { RewardTemplateSourceType } from '../reward-template-source-type';
import { RewardBalanceFilter } from '../reward-balance-filter';
import _ from 'lodash';

@Singleton
@LogClass()
export class RewardBalanceRepository extends DBRepository {
    constructor(
        @Inject private readonly mapper: RewardBalanceMapper) {
        super();
    }

    public async getAll(filter?: RewardBalanceFilter): Promise<PagedResult<RewardBalance>> {
        const manager = await this.getManager();

        const query = manager.createQueryBuilder(RewardBalanceEntity, 'rewardBalance');

        if (filter) {
            if (filter.id)
                query.andWhere('id = :id', { id: filter.id });

            if (filter.userId)
                query.andWhere('userId = :userId', { userId: filter.userId });

            if (filter.type)
                query.andWhere('type IN (:...type)', { type: filter.type });

            if (filter.status)
                query.andWhere('status IN (:...status)', { status: filter.status });

            if (filter.dateFrom)
                query.andWhere('dateFrom >= :dateFrom', { dateFrom: filter.dateFrom });

            if (filter.dateTo)
                query.andWhere('dateTo <= :dateTo', { dateTo: filter.dateTo });

            if (filter.sourceType)
                query.andWhere('sourceType IN (:...sourceType)', { sourceType: filter.sourceType });

            if (filter.page && filter.pageSize) {
                query.skip((filter.page - 1) * filter.pageSize);
                query.take(filter.pageSize);
            }

            if (filter.order)
                query.orderBy(convertOrdering('rewardBalance', filter.order));
            else
                query.orderBy('rewardBalance.createTime', 'DESC');
        }

        const [entities, count] = await query.getManyAndCount();
        const page = filter?.page || 1;
        const pageSize = filter?.pageSize || count;
        const balances = entities.map(e => this.mapper.fromEntity(e));
        return new PagedResult(balances, count, page, pageSize);
    }

    public async get(id: number): Promise<RewardBalance | undefined> {
        const manager = await this.getManager();
        const entity = await manager.findOne(RewardBalanceEntity, { where: { id } });

        if (!entity)
            return undefined;

        return this.mapper.fromEntity(entity);
    }

    public async getAllByTypeAndStatus<TRewardBalance extends RewardBalance>(type: RewardBalanceType, status: RewardBalanceStatus, take?: number, skip?: number): Promise<TRewardBalance[]> {
        const manager = await this.getManager();
        const entities = await manager.find(RewardBalanceEntity, {
            where: {
                type,
                status
            },
            take,
            skip
        });

        return entities.map(e => this.mapper.fromEntity<TRewardBalance>(e));
    }

    public async getByStatusAndSourceType(userId: number, status: RewardBalanceStatus, sourceType: RewardTemplateSourceType): Promise<RewardBalance | undefined> {
        const manager = await this.getManager();
        const entity = await manager.findOne(RewardBalanceEntity, {
            where: {
                userId,
                status,
                sourceType
            }
        });

        if (!entity)
            return undefined;

        return this.mapper.fromEntity(entity);
    }

    public async getByStatusAndType(userId: number, type: RewardBalanceType, status: RewardBalanceStatus): Promise<RewardBalance | undefined> {
        const manager = await this.getManager();
        const entity = await manager.findOne(RewardBalanceEntity, {
            where: {
                userId,
                type,
                status
            }
        });

        if (!entity)
            return undefined;

        return this.mapper.fromEntity(entity);
    }

    public async getMany(ids: number[]): Promise<RewardBalance[]> {
        const manager = await this.getManager();
        const entities = await manager.find(RewardBalanceEntity, {
            where: {
                id: In(ids)
            }
        });

        if (entities.length === 0)
            return [];

        return entities.map(e => this.mapper.fromEntity(e));
    }

    public async getDailyAllocation(userId: number): Promise<DailyAllocationRewardBalance | undefined> {
        const manager = await this.getManager();
        const entity = await manager.createQueryBuilder(RewardBalanceEntity, 'rewardBalance')
            .where('userId = :userId', { userId })
            .andWhere('type = :type', { type: RewardBalanceType.DailyAllocation })
            .andWhere('status = :status', { status: RewardBalanceStatus.InProgress })
            .andWhere('count > 0')
            .getOne();

        if (!entity)
            return undefined;

        return this.mapper.fromEntity(entity) as DailyAllocationRewardBalance;
    }

    public async getOpenDailyAllocation(userId: number): Promise<DailyAllocationRewardBalance[]> {
        const manager = await this.getManager();
        const entities = await manager.createQueryBuilder(RewardBalanceEntity, 'rewardBalance')
            .where('userId = :userId', { userId })
            .andWhere('type = :type', { type: RewardBalanceType.DailyAllocation })
            .andWhere('status IN (:...status)', { status: [RewardBalanceStatus.InProgress, RewardBalanceStatus.Processing] })
            .getMany();

        if (!entities.length)
            return [];

        return _.map(entities, e => this.mapper.fromEntity(e));
    }

    public async getLatestForUser<T extends RewardBalance>(userId: number, type: RewardBalanceType, status: RewardBalanceStatus): Promise<T | undefined> {
        const manager = await this.getManager();
        const entity = await manager.createQueryBuilder(RewardBalanceEntity, 'rewardBalance')
            .where('userId = :userId', { userId })
            .andWhere('type = :type', { type })
            .andWhere('status = :status', { status })
            .orderBy('createTime', 'DESC')
            .getOne();

        if (!entity)
            return undefined;

        return this.mapper.fromEntity<T>(entity);
    }

    public async add(balance: NewRewardBalance): Promise<void> {
        const manager = await this.getManager();
        const entities = this.mapper.toEntity(balance);
        await manager.save(RewardBalanceEntity, entities);
    }

    public async setStatus(status: RewardBalanceStatus, ids: number[]): Promise<void> {
        const manager = await this.getManager();
        await manager.update(RewardBalanceEntity, ids, { status });
    }

    public async upsert(userId: number, ...balances: NewRewardBalance[]): Promise<RewardBalance[]> {
        const manager = await this.getManager();
        const entities = balances.map(b => this.mapper.toEntity(b));
        const ids: number[] = [];

        await manager.transaction(async (transactionalEntityManager) => {
            for (const entity of entities) {
                const entityType = this.getEntityType(entity.type);

                const existingBalance = await transactionalEntityManager.findOne(entityType, {
                    where: {
                        userId,
                        type: entity.type,
                        status: entity.status,
                        dateTo: entity.dateTo
                    },
                    lock: {
                        mode: 'pessimistic_write'
                    }
                });

                if (existingBalance)
                    entity.id = existingBalance.id;

                const savedBalance = await transactionalEntityManager.save(entityType, entity);
                ids.push(savedBalance.id);
            }
        });

        if (!ids.length)
            return [];

        return await this.primary(r => r.getMany(ids)) as RewardBalance[];
    }

    public async updateDailyCount(id: number): Promise<DailyAllocationRewardBalance> {
        const manager = await this.getManager();
        await manager.createQueryBuilder()
            .update(RewardBalanceEntity)
            .set({ count: () => 'count - 1', })
            .where('id = :id', { id })
            .execute();

        return await this.primary(r => r.get(id)) as DailyAllocationRewardBalance;
    }

    private getEntityType(type: RewardBalanceType): typeof RewardBalanceEntity {
        switch (type) {
            case RewardBalanceType.WinBack:
                return WinBackRewardBalanceEntity;

            case RewardBalanceType.Daily:
                return DailyRewardBalanceEntity;

            case RewardBalanceType.DailyAllocation:
                return DailyAllocationRewardBalanceEntity;

            case RewardBalanceType.Weekly:
                return WeeklyRewardBalanceEntity;

            case RewardBalanceType.Monthly:
                return MonthlyRewardBalanceEntity;

            case RewardBalanceType.RankUp:
                return RankUpRewardBalanceEntity;

            default:
                throw new Error(`Reward balance type ${type} is not supported.`);
        }
    }
}