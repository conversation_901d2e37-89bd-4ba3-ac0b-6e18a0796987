import { <PERSON>ton } from '../../../../core/ioc';
import { BigNumber } from '../../../../core';
// import { RewardBalanceManager } from '../../../reward-balance-manager';
// import { SchedulerTimers } from '../../scheduler';
import { RewardBonus } from '../reward-bonus.model';
import { RewardUnclaimed } from '../../../reward-unclaimed';
import { RewardBalanceType } from '../../../reward-balance-type';
import { RewardBalanceConfiguration } from '../../configuration';
// import moment from 'moment';

@Singleton
export class RewardBonusDefaultMapper {
    constructor(
    /*     @Inject private readonly rewardBalanceManager: RewardBalanceManager,
        @Inject private readonly schedulerTimers: SchedulerTimers */) {
    }

    // We don't want to map from the balance anymore, we will now display a countdown to the expire time
    public async map(userId: number, unclaimed: RewardUnclaimed[], balanceType: RewardBalanceType, configuration: RewardBalanceConfiguration): Promise<RewardBonus> {
        const total = unclaimed.reduce((acc, t) => acc.plus(t.sourceAmount || 0), new BigNumber(0));

        return {
            amount: total,
            source: balanceType,
            dateTo: unclaimed[0].expireTime, // TODO improve this
            claimable: true
        };
    }


    // Will keep the method
    // private async mapFromBalance(userId: number, balanceType: RewardBalanceType, configuration: RewardBalanceConfiguration): Promise<RewardBonus> {
    //     const currentBalance = await this.rewardBalanceManager.getByUserId(userId, balanceType);
    //     const amount = currentBalance ? currentBalance.rewardAmount : new BigNumber(0);
    //     const now = moment().utc().startOf('minute').toDate();
    //     const showDateTo = [
    //         RewardBalanceType.WinBack,
    //         RewardBalanceType.Weekly,
    //         RewardBalanceType.Monthly
    //     ].includes(balanceType);

    //     const dateTo = (showDateTo && currentBalance && configuration) ? this.schedulerTimers.scheduleTime(now, configuration) : undefined;
    //     const showAmount = [RewardBalanceType.WinBack].includes(balanceType) && configuration;

    //     return {
    //         source: balanceType,
    //         amount: showAmount ? new BigNumber(amount) : undefined,
    //         dateTo,
    //         claimable: false
    //     };
    // }
}