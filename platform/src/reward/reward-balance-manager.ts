import { Inject, Singleton } from '../core/ioc';
import Logger, { LogClass } from '../core/logging';
import { BadRequestError, NotFoundError, PagedResult } from '../core';
import { RewardBalanceCache } from './cache';
import { NewRewardBalance } from './new-reward-balance';
import { RewardBalanceRepository } from './repositories/reward-balance.repository';
import { RewardBalanceStatus } from './reward-balance-status';
import { RewardBalanceType } from './reward-balance-type';
import { DailyAllocationRewardBalance, RewardBalance } from './reward-balance';
import { RewardTemplateSourceType } from './reward-template-source-type';
import { RewardBalanceFilter } from './reward-balance-filter';

@Singleton
@LogClass()
export class RewardBalanceManager {
    constructor(
        @Inject private readonly repository: RewardBalanceRepository,
        @Inject private readonly cache: RewardBalanceCache) {
    }

    public async getAll(filter?: RewardBalanceFilter): Promise<PagedResult<RewardBalance>> {
        return this.repository.getAll(filter);
    }

    public async get(id: number): Promise<RewardBalance | undefined> {
        return this.repository.get(id);
    }

    public async getByUserId<TRewardBalance extends RewardBalance>(userId: number, balanceType: RewardBalanceType): Promise<TRewardBalance | undefined> {
        const cached = await this.cache.get<TRewardBalance>(userId, balanceType);

        if (cached)
            return cached;

        const balance = await this.repository.getByStatusAndType(userId, balanceType, RewardBalanceStatus.InProgress);

        if (balance)
            await this.cache.store(balance);

        return balance as TRewardBalance;
    }

    public async getCachedByUserId<TRewardBalance extends RewardBalance>(userId: number, balanceType: RewardBalanceType): Promise<TRewardBalance | undefined> {
        const cached = await this.cache.get<TRewardBalance>(userId, balanceType);

        if (!cached)
            return undefined;

        return cached as TRewardBalance;
    }

    public async getAllByTypeAndStatus<TRewardBalance extends RewardBalance>(type: RewardBalanceType, status: RewardBalanceStatus, take?: number): Promise<TRewardBalance[]> {
        let skip = 0;
        let data: TRewardBalance[] = [];
        const results: TRewardBalance[] = [];
        const batchSize = take ?? 1000;

        do {
            Logger.info(`Fetching chunks between ${skip} and ${(skip + batchSize)}...`);
            data = await this.repository.getAllByTypeAndStatus<TRewardBalance>(type, status, take, skip);
            Logger.info(`Found ${data.length} data items...`);
            results.push(...data);
            skip += batchSize;
        }
        while (data.length >= batchSize);
        return results;
    }

    public async getAllForUser(userId: number): Promise<RewardBalance[]> {
        return this.cache.getAllForUser(userId);
    }

    public async getMany(ids: number[]): Promise<RewardBalance[]> {
        return this.repository.getMany(ids);
    }

    public async getByStatusAndSourceType(userId: number, status: RewardBalanceStatus, sourceType: RewardTemplateSourceType): Promise<RewardBalance | undefined> {
        return this.repository.getByStatusAndSourceType(userId, status, sourceType);
    }

    public async getByStatusAndType(userId: number, type: RewardBalanceType, status: RewardBalanceStatus): Promise<RewardBalance | undefined> {
        return this.repository.getByStatusAndType(userId, type, status);
    }

    public async getDailyAllocation(userId: number): Promise<DailyAllocationRewardBalance | undefined> {
        return this.repository.getDailyAllocation(userId);
    }

    public async getOpenDailyAllocation(userId: number): Promise<DailyAllocationRewardBalance[]> {
        return this.repository.getOpenDailyAllocation(userId);
    }

    public async getLatestForUser<T extends RewardBalance>(userId: number, type: RewardBalanceType, status: RewardBalanceStatus): Promise<T | undefined> {
        return this.repository.getLatestForUser<T>(userId, type, status);
    }

    public async upsert(userId: number, ...data: NewRewardBalance[]): Promise<RewardBalance[]> {
        const balances = await this.repository.upsert(userId, ...data);
        await this.cache.store(...balances);
        return balances;
    }

    public async setStatus(status: RewardBalanceStatus, ...ids: number[]): Promise<void> {
        await this.repository.setStatus(status, ids);
    }

    public async setClosed(id: number, userId: number, type: RewardBalanceType): Promise<void> {
        const balance = await this.get(id);

        if (balance)
            await this.setStatus(RewardBalanceStatus.Closed, id);

        const cachedBalance = await this.getByUserId(userId, type);

        if (cachedBalance)
            await this.cache.remove(userId, type);
    }

    public async updateDailyCount(id: number): Promise<DailyAllocationRewardBalance | undefined> {
        const allocation = await this.get(id);

        if (!allocation)
            throw new NotFoundError(`Reward balance with reference ${id} not found`);

        if (allocation.type !== RewardBalanceType.DailyAllocation)
            throw new BadRequestError(`Reward balance with reference ${id} is not a daily allocation`);

        if (allocation.count === 0)
            return;

        await this.cache.updateDailyAllocation(allocation.userId, id, allocation.type);
        return this.repository.updateDailyCount(id);
    }

    public async remove(userId: number, balanceType: RewardBalanceType): Promise<void> {
        await this.cache.remove(userId, balanceType);
    }
}